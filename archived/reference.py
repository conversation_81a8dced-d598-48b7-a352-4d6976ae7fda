import time
from logging import Logger
from typing import Any

from aiohttp import ClientSession
from dependency_injector.containers import DynamicContainer
from dependency_injector.wiring import inject, Provide

from dags.data_pipeline.containers import LoggerContainer, QueueContainer, FieldNameExtractor, IssueFieldsContainer
from dags.data_pipeline.utility_code import data_load, handle_exception, fetch_with_retries_post


@inject
async def get_issues_from_jira(
        producer_id: int, project_key: str, scope: str,
        # queue_issues: Queue, queue_stats: Queue,
        batch_start_time, http_session: ClientSession, url: str, name: str,
        my_logger: Logger = Provide[LoggerContainer.logger],
        # queue_issues: asyncio.Queue = Provide[QueueContainer.queue_issues],
        # queue_stats: asyncio.Queue = Provide[QueueContainer.queue_stats],
        q_container: DynamicContainer = Provide[QueueContainer],
        fields_container: FieldNameExtractor = Provide[IssueFieldsContainer.field_name_extractor]
):
    global commit_transaction
    q_container.config.override({"schema_name": f"{project_key}"})
    # active_queues = q_container.active_queues(project_key)

    if scope == "project":
        if data_load.get() == "initial":
            jql = f"""
                        project={project_key.upper()} 
                        and created > '{batch_start_time.strftime('%Y-%m-%d %H:%M')}' 
                        order by created asc
                    """
        else:
            jql = f"""
                project = {project_key.upper()} and updated > '{batch_start_time.strftime('%Y-%m-%d %H:%M')}'   
                order by updated asc
            """
    elif scope == "recon":
        jql = f"""
        project = {project_key.upper()} and issuetype = 'Initiative '
        """
    else:
        jql = None

    # Normalize whitespace by splitting and joining
    jql = ' '.join(jql.split())

    my_logger.debug(f"JQL = {jql}")

    # with open("field_list.json", "rb") as fp:
    #     file_content = fp.read()
    # fields: list = orjson.loads(file_content)['fields']
    try:
        fields: list = fields_container.get_field_names()
    except Exception as e:
        handle_exception(e)


    loop_count = 0

    # async with aiohttp.ClientSession() as http_session:

    try:
        while True:
            start_time_local = time.time_ns()
            start_time_process = time.process_time_ns()
            issue_start_at = (producer_id * 100) + (1000 * loop_count)

            response = await fetch_issues(
                http_session, url, jql, fields,
                start_at=issue_start_at
            )
            my_logger.debug(f"producer response is {response}")

            if response is None:
                await q_container.queue_selector()["queue_issues"].put(None)
                # await queue_issues.put(None)
                # queue_stats
                await q_container.queue_selector()["queue_stats"].put(
                    {
                        'isLast': True,
                        'process_time': 0,
                        'elapsed_time': 0,
                        'record_count': 0,
                        'total': 0,
                        'producer_id': producer_id,
                        'name': name,
                        'startAt': issue_start_at
                    }
                )
                break

            record_counts = len(response['result']['issues'])
            if record_counts > 0:
                # await queue_issues.put(response['issues'])
                await q_container.queue_selector()["queue_issues"].put(response['result']['issues'])

            total_time = time.time_ns() - start_time_local
            elapsed_time = time.process_time_ns() - start_time_process
            await q_container.queue_selector()["queue_stats"].put(
                {
                    'isLast': record_counts < response['result']['maxResults'],
                    'process_time': total_time,
                    'elapsed_time': elapsed_time,
                    'record_count': record_counts,
                    'total': response['result']['total'],
                    'producer_id': producer_id,
                    'name': name,
                    'startAt': issue_start_at
                }
            )
            if record_counts < response['result']['maxResults']:
                await q_container.queue_selector()["queue_issues"].put(None)
                my_logger.debug(f"{loop_count} breaking from loop")
                break

            loop_count += 1

    except Exception as e:
        handle_exception(e)
    finally:
        my_logger.info(f"Completed get_issues_from_jira")


async def fetch_issues(
        http_session: ClientSession, url, jql: str, fields: list,

        start_at: int = 0, max_results: int = 100
) -> Any:
    payload = {
        "jql": jql,
        "fields": fields,
        "startAt": start_at,
        "maxResults": max_results,
        "expand": ["changelog,renderedFields"]
    }
    try:
        return await fetch_with_retries_post(http_session, url, payload)
    except Exception as e:
        handle_exception(e)
